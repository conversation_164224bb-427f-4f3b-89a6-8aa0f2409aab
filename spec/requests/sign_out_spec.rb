require "rails_helper"
require "spec_access_helper"

RSpec.describe "session/sign_out", type: :request do
  let(:user) { FactoryBot.create(:user) }

  context "when fusionauth JWT feature flag is disabled" do
    before do
      allow_any_instance_of(SessionsController).to receive(:fusionauth_jwt_post_signin_enabled?).and_return(false)
    end

    it "should redirect to the Culture Amp sign-out page and revoke session access" do
      delete "/session/sign_out", as: :json

      expect(response.status).to eq(204)

      get "/my/user-settings"
      expect(response.status).to eq(302)
      expect(response.headers["Location"]).to include("/session/sign_in?redirect=")
    end

    context "when headers are provided" do
      before do
        sign_in(user)
      end

      it "should redirect to the Culture Amp sign-out page and revoke session access" do
        delete "/session/sign_out", as: :json

        expect(response.status).to eq(204)

        get "/my/user-settings"
        expect(response.status).to eq(302)
        expect(response.headers["Location"]).to include("/session/sign_in?redirect=")
      end
    end
  end

  context "when fusionauth JWT feature flag is enabled" do
    # Real JWT configuration using the test setup
    let(:jwt_sid) { "fake-jwt-sid" }
    let(:payload) { {"effectiveUserId" => user.aggregate_id, "accountId" => user.account.aggregate_id, "realUserId" => user.aggregate_id, "sid" => jwt_sid} }
    let(:web_gateway_equivalent_key) { Rails.application.config.test_jwt_private_key }
    let(:token) { JWT.encode(payload, web_gateway_equivalent_key, "RS256") }
    let(:headers) { {"X-CA-FA-Authorization" => "Bearer #{token}"} }

    before do
      allow_any_instance_of(SessionsController).to receive(:fusionauth_jwt_post_signin_enabled?).and_return(true)
      allow_any_instance_of(SessionsController).to receive(:sign_out_from_fusionauth)
    end

    it "returns no_content status and signs out from FusionAuth with headers and use json request" do
      delete "/session/sign_out", headers: headers, as: :json

      expect(response).to have_http_status(:no_content)
    end

    it "returns no content status and signs out from FusionAuth without headers and use json request" do
      delete "/session/sign_out", headers: {}, as: :json

      expect(response).to have_http_status(:no_content)
    end

    it "returns redirect status and signs out from FusionAuth with headers and use html request" do
      delete "/session/sign_out", headers: headers

      expect(response).to have_http_status(:redirect)
      expect(response.headers["Location"]).to eq("https://www.cultureamp.com/sign_out")
    end

    it "returns redirect status and signs out from FusionAuth without headers and use html request" do
      delete "/session/sign_out", headers: {}

      expect(response).to have_http_status(:redirect)
      expect(response.headers["Location"]).to eq("https://www.cultureamp.com/sign_out")
    end
  end
end
