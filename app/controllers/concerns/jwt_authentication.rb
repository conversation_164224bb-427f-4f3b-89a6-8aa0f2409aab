module JwtAuthentication
  extend ActiveSupport::Concern

  include Authentication::FusionAuthHelper

  EffectiveUserIdError = Class.new(StandardError)
  RealUserIdError = Class.new(StandardError)
  AccountIdError = Class.new(StandardError)
  SidError = Class.new(StandardError)
  JWTDecoderError = Class.new(StandardError)

  JWT_ALGORITHM = "RS256"

  def jwt_user_id
    # aggregate id of the user, or the user that the user is masquerading as
    effective_user_id = jwt_payload.fetch("effectiveUserId", nil)
    raise(EffectiveUserIdError, "JWT verification error - effectiveUserId") if effective_user_id.blank?

    effective_user_id
  end

  def jwt_real_user_id
    real_user_id = jwt_payload.fetch("realUserId", nil) # aggregate id of the actual user
    raise(RealUserIdError, "JWT verification error - realUserId") if real_user_id.blank?

    real_user_id
  end

  def jwt_account_id
    account_id = jwt_payload.fetch("accountId", nil) # aggregate id of the account
    raise(AccountIdError, "JWT verification error - accountId") if account_id.blank?

    account_id
  end

  def jwt_sid
    sid = jwt_payload.fetch("sid", nil)
    raise(SidError, "JWT verification error - sid") if sid.blank?

    sid
  end

  def decode_jwt_using_jwk(jwt)
    jwks = JSON.parse(ENV["FUSION_AUTH_JWKS_JSON"])
    decode_options = {
      algorithms: ["RS256", "RS384", "RS512", "ES256", "ES384", "ES512"],
      jwks: jwks
    }

    JWT.decode(
      jwt,
      nil,
      true,
      decode_options
    ).first
  end

  def jwt_payload
    unless @_payload
      begin
        payload, _header = JWT.decode(jwt, jwt_pubkey, true, algorithm: JWT_ALGORITHM)
        @_payload ||= payload
      rescue JWT::DecodeError, JWT::VerificationError
        begin
          jwt_payload_decoded_by_jwk = decode_jwt_using_jwk(jwt)
          @_payload = jwt_payload_decoded_by_jwk
        rescue => e
          Rails.logger.error("Unable to decode JWT: #{e.message}")
          raise JWTDecoderError, "Unable to decode JWT: #{e.message}"
        end
      end
    end
    @_payload
  end

  def jwt
    auth_header = get_jwt_from_request_header

    return nil unless auth_header

    matches = auth_header.match(/Bearer (?<token>.*)/)
    return nil unless matches

    @_jwt = matches[:token]
  end

  # This is only being used for sending requests from murmur to other external services through http-clients
  def get_auth_header
    request.headers.fetch("Authorization", nil).presence || request.headers.fetch("X-CA-SGW-Authorization", nil)
  end

  def jwt_pubkey
    pubkey = ENV["AUTH_JWT_PUBKEY"]
    raise "Missing ENV variable AUTH_JWT_PUBKEY" if pubkey.blank?

    # ENV auto-escapes \n characters; gsub here puts them back, giving a valid pubkey
    pubkey = pubkey.gsub('\\n', "\n")
    OpenSSL::PKey::RSA.new(pubkey)
  end

  private

  def get_jwt_from_request_header
    if fusionauth_jwt_post_signin_enabled?(get_subdomain_from_request)
      request.headers.fetch("X-CA-FA-Authorization", nil).presence ||
        request.headers.fetch("Authorization", nil)
    else
      request.headers.fetch("X-CA-SGW-Authorization", nil).presence ||
        request.headers.fetch("Authorization", nil)
    end
  end
end
